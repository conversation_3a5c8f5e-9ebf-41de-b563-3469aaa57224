{"GlobalPropertiesHash": "CdOz6nhJLZhf3tr3eZRk3k1Dp5Sklv9oK8T6Uw2b77U=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["E1CWyPxQmA7Aqv9Nul/usheAT7of1Gi26IQZPov1CF0=", "5gfigNgSGyb2IysfwKgWhfpY0In9gNIBFDtylPGcexE=", "S7htN4TNYs32i6ILcpPGPrYPjVPidos3DiAiW4gV4a8=", "kMasFai9qXrVJjcIPPBnejIU7vPF8/iMu9Sv9Xea5o4=", "imrQ96Sii+H4S0IMNhJHml8U7Kjdktf1WVNq1WtDkEM=", "CDb+T0s5blLTXTAW3COAy8V6R+8B2sk2e2TPR3ElRwU=", "dSAupCyacDTKMlrhrOv+BnWJYPeT+/bl/Wm6LLzuQEs=", "wj0/E4GNubNqVDW66rqVMnlTILysbDUE3j7Tt0p5WL4=", "UFSOvvmvokTv0+fjINsCWasflN58iAjanrm/Ord8pFE=", "on934VjnJoAGVUVn4bfEJsw9cK5rMexCyL9y+8ca3es=", "NEsbSt29yVoZaYBvuKRg4BmdDM3vh+I8qhIKjcl9GNs=", "YgV9q10fIT51WPvQWDFNzN83Y1O8F+c30a2uY9Ipzo0=", "QvRIq7k6qeGOCnqZGB6N7PHHZuG5dupeyrvTf+78/Eo=", "APi1eoPGlOmjyyd7KbKPMUYHLj9htwJROvHLapGv8K8=", "v8XY47BQaqHAsf1bzC9MYw2G6QRRe983praAskarobw=", "sVJhdbOrsnq4vYechhPi35E5GH0nD/KWTwm6EpZ6uF8=", "RgnY9IuTYfB/tuvZ9NQifPVvz8EsxXU9ERfbCQqBrcA=", "xh6NEyEkENt6nMz8peOPwk5sBl9sk1qbYERmT/GyOoA=", "FvENnMEhngDVojxZhoKpyh6JD06evAhaIm+ArJRgMOU=", "8iwfSSpV54Cp5k6Nk7uSlUA5CqahaiBjWkgEsJwvNZw=", "ERwdc3k4/+sjlIhSRT+JqoAiukbVY27JnCyHvDYgQ7c=", "BrsXkSN5v/Xk15+uAJdBm2+hlCa5150XmGzFeR7x0QU=", "SffXhLH7TEQdpXcQh2h8p17hGuOAiXuPYCvJolSWeTc=", "yy9Ryl3MDZijgI4kx3Z/0XmttDD4fOO9f5mkf+2EQHw=", "Xo934siis4THJ7byLQtoqwyLIOSg2YmGUxBkl/jtC+k=", "Eyr6dCRwZYK0E4fZAWsVxtHfqIGGey2w6Flxdxrq6Do=", "UCu8lZLWljfbCMSr/n45Uv0FYRvW674+trB0rMa2fNQ=", "+KtYTalgA2Iqi7rhHQDjEbjevxKYnmmjEDTxYjjubhU=", "XOmrWvZnsujo6ickcrxrmEvX0jk/i5ji0QUUIj1etd0=", "KCbIIJ4iwSqie3zL73rkoWMOknGSnJfT4C7pJAyCUa4=", "e5V4BhxFnTKbM8WTwV336JPiZNwJkA2DjdzmosAtE74=", "BxY2y23zSInNAsLmckjVy+WIin2AZlWa1Svr1j80eQE=", "+FJfKcv/LgevI2DlJs1BlffSIbaG9Zik/t4hZuc57A4=", "zS3wDiyMWQmgpeDmkuCxfWFgEdEMbP+aRZCP20oejMI=", "rOOEt1qzBDR0AjXwpx+kMGHhtH2TNjpk2k3rMPthBq8=", "Wj4/FEBGU9Yh073KvSLWjA5VmGSn5x6wz2LjVfA0U84=", "DptGUvIwUmCEEl6YY5HqXZmQRJiIpoY+wkf1CdD8R8Q=", "uh2kkKWITQxOdduGezKcb3FwM1Qh2WKRGwFzdryJF1I=", "BgbIHvi+qpfHzb8E9YSnorrzHiEJDfnV7pRWosGygcU=", "0Y9aVSnRIWvZ1mTVc11ypir/u2coXCuRMMdur1ABqXg=", "PSeY6BZQB7+/votmaqpPU27d8PDkOZhpBD08zKvQbEc=", "/bcdSOkvHOOCZbkkyolVaWS8+WeHoFCM5+ZukuKcyrM=", "OjfZdg/nkTjBGSTunZA4P3Tetj8Zk/OevCwPw3LdvmA=", "u4OxvJcGK3uedj1BzUMfnX8y3zb62zG6BjM1h77/VAY=", "FBjlOPAxTQQ9zoVgxzt+r6GUOA5I3x13omTrQ0J0KSE=", "iJrSqzVM9b47HE64+XAovRBRGtrl2ZF35y3CuvXcSGE=", "+aTU0A1DXIhWbUN87jLsUmCwQslqd9zMnkczX0hK4Cg=", "reeKpfnuQh2N1of+09hCZdE7gNeVDlpzMFpQE0amcps=", "FkRXHl7r5jkyQMVMTbhhcGGzl3rxydFd/UpLzrjdbZc=", "5CSYyYkVnkJTbRXQOcyTonhfPo/ENORbIvFxQ+uVgJU=", "0x61L0tsVSSEADqdK74yUnJtdt5wAV1PabRSS5NpMXg=", "aCyhBJasSwO/pMcD7tM4el/U4LIZEobyidDk/AOFWt4=", "id9+li/mZVFTJvYOvpWJW0MZEVqPRPSidapCPWkAKsg=", "6JNkNJtEhMF9cxs1khhY4hU784V39hrp2/DtkdKQzyI=", "ggpTBUJYVtEobgdZs0uvW4WzSS50t5eATntgi/t4wO8=", "UV5/iWC1X6PtIbdJlzqXTA0ZKx+gILdzjdtHAyupxek=", "s7FM9Qvsdvi+5CBYUJG3bLgSc451D/JAilyQfDerUsU=", "Ll6bimk7/XDI60u5GsWGr0X6+ho+RL7Z67yijRfgm+8=", "w0Fss9yq0ID3HLF7LsjtlqMKTe10mz7WeAtAoK5dlC4=", "ijAeHNVgJ3aWHrTBldSzcclbxYHhOwntAgwhjo6kBJc=", "f8swu85dhgZO29yqpDJTNfZlmt+w3tzQa1vxWf2LfJE=", "LCVAanDaH3OEqne/KKGnXbS6gU5j+dQH8Z/+9ZwW8yo=", "x1XHbpHqDEoinms8bfxuq87XXpxS8mXmvD1RwwU/0bo=", "yb+bfgznpIXRw6LK0ocsB6A8BDjIgtI51eA+SF6f+y8=", "749qx2l2lIQV2fPoJoQ8KqoQiKihtx21zNf/8owbJdE=", "u8b0+VB+E41VAoUm8Q25n8v+xi5FICfH8ekLKpCtAH0=", "EV4OJHntSxOBucyI1/R1lr137mnvpCk5MPNy+arWMPc=", "yQigk2QMdO4lWX7mtGY8Dnd5/kkb2x7oleOw6Cgvbzg=", "rAjRIEIAdh7xlfu/Mr0CzdqroavXK0typTz51Fqukkw=", "gGzSOzvlfg6D3eeUNBJj2CvsqF4aEI2xO8YPoyWx1eQ=", "mGodviyL/THlavsnYIFTM5G/lS78pxZUrKetg/QZn5w=", "pPSx6eoyboejKNxLCRDm5qlCHQYlswBiTAQB/SDL49c=", "7uN3c4qgqwvBt+AGajOq2m51os2kX6MKcWZYAfkLKS8="], "CachedAssets": {"E1CWyPxQmA7Aqv9Nul/usheAT7of1Gi26IQZPov1CF0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\css\\site.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-06-28T15:08:22.3557824+00:00"}, "5gfigNgSGyb2IysfwKgWhfpY0In9gNIBFDtylPGcexE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\favicon.ico", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-06-28T15:08:22.3063678+00:00"}, "S7htN4TNYs32i6ILcpPGPrYPjVPidos3DiAiW4gV4a8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\js\\site.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-28T15:08:22.3688445+00:00"}, "kMasFai9qXrVJjcIPPBnejIU7vPF8/iMu9Sv9Xea5o4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-28T15:08:22.1799873+00:00"}, "imrQ96Sii+H4S0IMNhJHml8U7Kjdktf1WVNq1WtDkEM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-28T15:08:22.1799873+00:00"}, "CDb+T0s5blLTXTAW3COAy8V6R+8B2sk2e2TPR3ElRwU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-28T15:08:22.1799873+00:00"}, "dSAupCyacDTKMlrhrOv+BnWJYPeT+/bl/Wm6LLzuQEs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-28T15:08:22.1799873+00:00"}, "wj0/E4GNubNqVDW66rqVMnlTILysbDUE3j7Tt0p5WL4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-28T15:08:22.1799873+00:00"}, "UFSOvvmvokTv0+fjINsCWasflN58iAjanrm/Ord8pFE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-28T15:08:22.1934922+00:00"}, "on934VjnJoAGVUVn4bfEJsw9cK5rMexCyL9y+8ca3es=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-28T15:08:22.1939439+00:00"}, "NEsbSt29yVoZaYBvuKRg4BmdDM3vh+I8qhIKjcl9GNs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-28T15:08:22.194451+00:00"}, "YgV9q10fIT51WPvQWDFNzN83Y1O8F+c30a2uY9Ipzo0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-28T15:08:22.194451+00:00"}, "QvRIq7k6qeGOCnqZGB6N7PHHZuG5dupeyrvTf+78/Eo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-28T15:08:22.195469+00:00"}, "APi1eoPGlOmjyyd7KbKPMUYHLj9htwJROvHLapGv8K8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "v8XY47BQaqHAsf1bzC9MYw2G6QRRe983praAskarobw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "sVJhdbOrsnq4vYechhPi35E5GH0nD/KWTwm6EpZ6uF8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "RgnY9IuTYfB/tuvZ9NQifPVvz8EsxXU9ERfbCQqBrcA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "xh6NEyEkENt6nMz8peOPwk5sBl9sk1qbYERmT/GyOoA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "FvENnMEhngDVojxZhoKpyh6JD06evAhaIm+ArJRgMOU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "8iwfSSpV54Cp5k6Nk7uSlUA5CqahaiBjWkgEsJwvNZw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "ERwdc3k4/+sjlIhSRT+JqoAiukbVY27JnCyHvDYgQ7c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "BrsXkSN5v/Xk15+uAJdBm2+hlCa5150XmGzFeR7x0QU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "SffXhLH7TEQdpXcQh2h8p17hGuOAiXuPYCvJolSWeTc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "yy9Ryl3MDZijgI4kx3Z/0XmttDD4fOO9f5mkf+2EQHw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "Xo934siis4THJ7byLQtoqwyLIOSg2YmGUxBkl/jtC+k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-28T15:08:22.1965646+00:00"}, "Eyr6dCRwZYK0E4fZAWsVxtHfqIGGey2w6Flxdxrq6Do=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-28T15:08:22.2060726+00:00"}, "UCu8lZLWljfbCMSr/n45Uv0FYRvW674+trB0rMa2fNQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-28T15:08:22.2065866+00:00"}, "+KtYTalgA2Iqi7rhHQDjEbjevxKYnmmjEDTxYjjubhU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-28T15:08:22.2065866+00:00"}, "XOmrWvZnsujo6ickcrxrmEvX0jk/i5ji0QUUIj1etd0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-28T15:08:22.2101591+00:00"}, "KCbIIJ4iwSqie3zL73rkoWMOknGSnJfT4C7pJAyCUa4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-28T15:08:22.2101591+00:00"}, "e5V4BhxFnTKbM8WTwV336JPiZNwJkA2DjdzmosAtE74=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-28T15:08:22.2101591+00:00"}, "BxY2y23zSInNAsLmckjVy+WIin2AZlWa1Svr1j80eQE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-28T15:08:22.2155711+00:00"}, "+FJfKcv/LgevI2DlJs1BlffSIbaG9Zik/t4hZuc57A4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-28T15:08:22.2155711+00:00"}, "zS3wDiyMWQmgpeDmkuCxfWFgEdEMbP+aRZCP20oejMI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-28T15:08:22.2210772+00:00"}, "rOOEt1qzBDR0AjXwpx+kMGHhtH2TNjpk2k3rMPthBq8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-28T15:08:22.2210772+00:00"}, "Wj4/FEBGU9Yh073KvSLWjA5VmGSn5x6wz2LjVfA0U84=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "DptGUvIwUmCEEl6YY5HqXZmQRJiIpoY+wkf1CdD8R8Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "uh2kkKWITQxOdduGezKcb3FwM1Qh2WKRGwFzdryJF1I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "BgbIHvi+qpfHzb8E9YSnorrzHiEJDfnV7pRWosGygcU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "0Y9aVSnRIWvZ1mTVc11ypir/u2coXCuRMMdur1ABqXg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "PSeY6BZQB7+/votmaqpPU27d8PDkOZhpBD08zKvQbEc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "/bcdSOkvHOOCZbkkyolVaWS8+WeHoFCM5+ZukuKcyrM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "OjfZdg/nkTjBGSTunZA4P3Tetj8Zk/OevCwPw3LdvmA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "u4OxvJcGK3uedj1BzUMfnX8y3zb62zG6BjM1h77/VAY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "FBjlOPAxTQQ9zoVgxzt+r6GUOA5I3x13omTrQ0J0KSE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "iJrSqzVM9b47HE64+XAovRBRGtrl2ZF35y3CuvXcSGE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-28T15:08:22.2255842+00:00"}, "+aTU0A1DXIhWbUN87jLsUmCwQslqd9zMnkczX0hK4Cg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-28T15:08:22.2412151+00:00"}, "reeKpfnuQh2N1of+09hCZdE7gNeVDlpzMFpQE0amcps=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-28T15:08:22.2155711+00:00"}, "FkRXHl7r5jkyQMVMTbhhcGGzl3rxydFd/UpLzrjdbZc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-28T15:08:22.3219582+00:00"}, "5CSYyYkVnkJTbRXQOcyTonhfPo/ENORbIvFxQ+uVgJU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-28T15:08:22.3219582+00:00"}, "0x61L0tsVSSEADqdK74yUnJtdt5wAV1PabRSS5NpMXg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-28T15:08:22.2210772+00:00"}, "aCyhBJasSwO/pMcD7tM4el/U4LIZEobyidDk/AOFWt4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-06-28T15:08:22.2574569+00:00"}, "id9+li/mZVFTJvYOvpWJW0MZEVqPRPSidapCPWkAKsg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-06-28T15:08:22.3063678+00:00"}, "6JNkNJtEhMF9cxs1khhY4hU784V39hrp2/DtkdKQzyI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-06-28T15:08:22.3214492+00:00"}, "ggpTBUJYVtEobgdZs0uvW4WzSS50t5eATntgi/t4wO8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-06-28T15:08:22.3219582+00:00"}, "UV5/iWC1X6PtIbdJlzqXTA0ZKx+gILdzjdtHAyupxek=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-28T15:08:22.2210772+00:00"}, "s7FM9Qvsdvi+5CBYUJG3bLgSc451D/JAilyQfDerUsU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-06-28T15:08:22.242374+00:00"}, "Ll6bimk7/XDI60u5GsWGr0X6+ho+RL7Z67yijRfgm+8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-06-28T15:08:22.242374+00:00"}, "w0Fss9yq0ID3HLF7LsjtlqMKTe10mz7WeAtAoK5dlC4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-06-28T15:08:22.242374+00:00"}, "ijAeHNVgJ3aWHrTBldSzcclbxYHhOwntAgwhjo6kBJc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-06-28T15:08:22.242374+00:00"}, "f8swu85dhgZO29yqpDJTNfZlmt+w3tzQa1vxWf2LfJE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-06-28T15:08:22.242374+00:00"}, "LCVAanDaH3OEqne/KKGnXbS6gU5j+dQH8Z/+9ZwW8yo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-06-28T15:08:22.2574569+00:00"}, "x1XHbpHqDEoinms8bfxuq87XXpxS8mXmvD1RwwU/0bo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "WebApplication1", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication1\\WebApplication1\\wwwroot\\", "BasePath": "_content/WebApplication1", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-28T15:08:22.2155711+00:00"}}, "CachedCopyCandidates": {}}